"""
10 Million Row Scale Comparison: Delta Lake Native vs DuckLake
Focused performance comparison at production scale.
"""

import os
import shutil
import time
import random
from pathlib import Path
import pyarrow as pa
import pyarrow.parquet as pq
import duckdb
from deltalake import DeltaTable, write_deltalake

from delta_merge_examples.shared_data_generator import (
    ParquetDatasetManager, 
    generate_transaction_data
)
from delta_merge_examples.performance_profiler import (
    DeltaLakeProfiler, 
    DuckLakeProfiler
)


class Scale10MComparison:
    """Compare Delta Lake Native vs DuckLake at 10M row scale."""
    
    def __init__(self):
        self.dataset_manager = ParquetDatasetManager()
        self.delta_path = "./scale_10m_delta"
        self.ducklake_path = "./scale_10m_ducklake"
        self.results = {}
    
    def setup_10m_dataset(self):
        """Set up 10M row dataset for testing."""
        dataset_name = "scale_10m_test"
        
        if not self.dataset_manager.dataset_exists(dataset_name):
            print("📦 Generating 10M row dataset (this may take a few minutes)...")
            print("   🎯 Target: 5,000 files, 10,000,000 rows")
            print("   📊 Distribution: ~100 large files (50K-200K rows), 4,900 small files")
            
            metadata = self.dataset_manager.generate_dataset(
                dataset_name=dataset_name,
                total_files=5000,
                total_rows=10000000,
                large_file_ratio=1/50  # 2% large files, 98% small files
            )
        else:
            print("📦 Loading existing 10M row dataset...")
            metadata = self.dataset_manager.load_dataset_metadata(dataset_name)
        
        print(f"Dataset ready: {len(metadata['parquet_files']):,} files")
        large_files = [f for f in metadata['parquet_files'] if f['is_large']]
        small_files = [f for f in metadata['parquet_files'] if not f['is_large']]
        print(f"Distribution: {len(large_files)} large files, {len(small_files)} small files")
        
        return metadata
    
    def setup_delta_table(self, metadata):
        """Set up Delta Lake table from 10M row dataset."""
        if os.path.exists(self.delta_path):
            shutil.rmtree(self.delta_path)
        
        print("🔺 Setting up Delta Lake table (10M rows)...")
        start_time = time.time()
        
        # Load in larger batches for efficiency
        batch_size = 200  # Larger batches for 10M scale
        total_files = len(metadata['parquet_files'])
        
        for batch_start in range(0, total_files, batch_size):
            batch_end = min(batch_start + batch_size, total_files)
            if batch_start % 1000 == 0:  # Progress updates
                print(f"   📦 Loading batch: files {batch_start:,}-{batch_end-1:,}")
            
            batch_files = metadata['parquet_files'][batch_start:batch_end]
            parquet_paths = [f['parquet_path'] for f in batch_files]
            
            # Read and combine Parquet files
            tables = [pq.read_table(path) for path in parquet_paths]
            combined_table = pa.concat_tables(tables)
            
            # Write to Delta table
            mode = "overwrite" if batch_start == 0 else "append"
            write_deltalake(self.delta_path, combined_table, mode=mode)
        
        setup_time = time.time() - start_time
        dt = DeltaTable(self.delta_path)
        total_rows = dt.to_pyarrow_table().num_rows
        
        print(f"Delta table ready: {total_rows:,} rows in {setup_time:.1f}s")
        return dt
    
    def setup_ducklake_table(self, metadata):
        """Set up DuckLake table from 10M row dataset."""
        # Clean up existing
        if os.path.exists(self.ducklake_path):
            if os.path.isfile(self.ducklake_path):
                os.remove(self.ducklake_path)
            else:
                shutil.rmtree(self.ducklake_path)
        
        print("🦆 Setting up DuckLake table (10M rows)...")
        start_time = time.time()
        
        # Initialize DuckDB connection
        conn = duckdb.connect()
        conn.execute("INSTALL ducklake;")
        conn.execute("LOAD ducklake;")
        conn.execute(f"ATTACH 'ducklake:{self.ducklake_path}' AS scale_lake;")
        conn.execute("USE scale_lake;")
        
        # Create table schema
        conn.execute("""
            CREATE TABLE demo (
                file_id VARCHAR,
                customer_id VARCHAR,
                transaction_id VARCHAR,
                amount DOUBLE,
                transaction_date TIMESTAMP,
                processed_at TIMESTAMP
            );
        """)
        
        # Load data in batches
        batch_size = 200
        total_files = len(metadata['parquet_files'])
        
        for batch_start in range(0, total_files, batch_size):
            batch_end = min(batch_start + batch_size, total_files)
            if batch_start % 1000 == 0:  # Progress updates
                print(f"   📦 Loading batch: files {batch_start:,}-{batch_end-1:,}")
            
            batch_files = metadata['parquet_files'][batch_start:batch_end]
            
            for file_info in batch_files:
                parquet_path = file_info['parquet_path']
                conn.execute(f"INSERT INTO demo SELECT * FROM read_parquet('{parquet_path}');")
        
        setup_time = time.time() - start_time
        result = conn.execute("SELECT COUNT(*) FROM demo;").fetchone()
        total_rows = result[0] if result else 0
        
        print(f"DuckLake table ready: {total_rows:,} rows in {setup_time:.1f}s")
        return conn
    
    def test_delta_native_performance(self, dt, metadata):
        """Test Delta Lake native operations at 10M scale."""
        print(f"\n{'='*80}")
        print("DELTA LAKE NATIVE OPERATIONS - 10M SCALE")
        print(f"{'='*80}")
        
        profiler = DeltaLakeProfiler(self.delta_path)
        
        # Test with different file sizes
        test_scenarios = [
            ("small", [f for f in metadata['parquet_files'] if not f['is_large']][:3]),
            ("large", [f for f in metadata['parquet_files'] if f['is_large']][:3])
        ]
        
        results = {}
        
        for scenario_name, test_files in test_scenarios:
            print(f"\n🎯 Testing {scenario_name} file operations...")
            scenario_results = []
            
            for file_info in test_files:
                file_id = file_info['file_id']
                original_size = file_info['row_count']
                new_size = max(100, int(original_size * 0.9))  # 10% smaller
                
                print(f"   📄 File: {file_id} ({original_size:,} → {new_size:,} rows)")
                
                # Generate new data
                new_df = generate_transaction_data(file_id, new_size)
                new_data = pa.Table.from_pandas(new_df)
                
                # Profile DELETE + APPEND
                with profiler.profile_operation(f"Delta Native {scenario_name} - {file_id}"):
                    # DELETE
                    delete_start = time.time()
                    dt.delete(predicate=f"file_id = '{file_id}'")
                    delete_time = time.time() - delete_start
                    
                    # APPEND
                    append_start = time.time()
                    write_deltalake(self.delta_path, new_data, mode="append")
                    append_time = time.time() - append_start
                
                scenario_results.append({
                    'file_id': file_id,
                    'original_size': original_size,
                    'new_size': new_size,
                    'delete_time': delete_time,
                    'append_time': append_time,
                    'total_time': delete_time + append_time,
                    'profile': profiler.current_profile
                })
                
                print(f"      DELETE: {delete_time:.3f}s, APPEND: {append_time:.3f}s, Total: {delete_time + append_time:.3f}s")
            
            results[scenario_name] = scenario_results
        
        self.results['delta_native'] = results
        return results
    
    def test_ducklake_performance(self, conn, metadata):
        """Test DuckLake operations at 10M scale."""
        print(f"\n{'='*80}")
        print("DUCKLAKE OPERATIONS - 10M SCALE")
        print(f"{'='*80}")
        
        profiler = DuckLakeProfiler(conn)
        
        # Test with different file sizes
        test_scenarios = [
            ("small", [f for f in metadata['parquet_files'] if not f['is_large']][:3]),
            ("large", [f for f in metadata['parquet_files'] if f['is_large']][:3])
        ]
        
        results = {}
        
        for scenario_name, test_files in test_scenarios:
            print(f"\n🎯 Testing {scenario_name} file operations...")
            scenario_results = []
            
            for file_info in test_files:
                file_id = file_info['file_id']
                original_size = file_info['row_count']
                new_size = max(100, int(original_size * 0.9))
                
                print(f"   📄 File: {file_id} ({original_size:,} → {new_size:,} rows)")
                
                # Generate new data
                new_df = generate_transaction_data(file_id, new_size)
                conn.register('new_data', new_df)
                
                # Profile transaction
                with profiler.profile_operation(f"DuckLake {scenario_name} - {file_id}"):
                    transaction_start = time.time()
                    
                    conn.execute("BEGIN TRANSACTION;")
                    
                    delete_start = time.time()
                    conn.execute(f"DELETE FROM demo WHERE file_id = '{file_id}';")
                    delete_time = time.time() - delete_start
                    
                    insert_start = time.time()
                    conn.execute("INSERT INTO demo SELECT * FROM new_data;")
                    insert_time = time.time() - insert_start
                    
                    commit_start = time.time()
                    conn.execute("COMMIT;")
                    commit_time = time.time() - commit_start
                    
                    total_time = time.time() - transaction_start
                
                conn.unregister('new_data')
                
                scenario_results.append({
                    'file_id': file_id,
                    'original_size': original_size,
                    'new_size': new_size,
                    'delete_time': delete_time,
                    'insert_time': insert_time,
                    'commit_time': commit_time,
                    'total_time': total_time,
                    'profile': profiler.current_profile
                })
                
                print(f"      DELETE: {delete_time:.3f}s, INSERT: {insert_time:.3f}s, COMMIT: {commit_time:.3f}s, Total: {total_time:.3f}s")
            
            results[scenario_name] = scenario_results
        
        self.results['ducklake'] = results
        return results
    
    def analyze_10m_results(self):
        """Analyze and compare 10M scale results."""
        print(f"\n{'='*80}")
        print("10M SCALE PERFORMANCE ANALYSIS")
        print(f"{'='*80}")
        
        if 'delta_native' not in self.results or 'ducklake' not in self.results:
            print("❌ Incomplete results for analysis")
            return
        
        # Compare by scenario
        for scenario in ['small', 'large']:
            print(f"\n📊 {scenario.upper()} FILE PERFORMANCE COMPARISON")
            print("-" * 60)
            
            delta_results = self.results['delta_native'][scenario]
            ducklake_results = self.results['ducklake'][scenario]
            
            # Calculate averages
            delta_avg = sum(r['total_time'] for r in delta_results) / len(delta_results)
            ducklake_avg = sum(r['total_time'] for r in ducklake_results) / len(ducklake_results)
            
            winner = "Delta Native" if delta_avg < ducklake_avg else "DuckLake"
            speedup = max(delta_avg, ducklake_avg) / min(delta_avg, ducklake_avg)
            
            print(f"Delta Native avg:  {delta_avg:.3f}s")
            print(f"DuckLake avg:      {ducklake_avg:.3f}s")
            print(f"Winner:           {winner} ({speedup:.2f}x faster)")
            
            # Detailed breakdown
            print(f"\nDetailed Results:")
            print(f"{'File Size':<12} {'Delta (s)':<12} {'DuckLake (s)':<12} {'Winner':<12}")
            print("-" * 50)
            
            for i, (delta_r, duck_r) in enumerate(zip(delta_results, ducklake_results)):
                size_label = f"{delta_r['original_size']:,}"
                delta_time = delta_r['total_time']
                duck_time = duck_r['total_time']
                file_winner = "Delta" if delta_time < duck_time else "DuckLake"
                
                print(f"{size_label:<12} {delta_time:<12.3f} {duck_time:<12.3f} {file_winner:<12}")
        
        # Overall summary
        print(f"\n10M SCALE SUMMARY:")
        print("1. Performance patterns at production scale")
        print("2. File size impact on operation speed")
        print("3. Memory usage characteristics")
        print("4. Scaling behavior differences")
        print("5. Transaction model differences (2 operations vs 1 transaction)")
    
    def cleanup(self):
        """Clean up test resources."""
        try:
            if os.path.exists(self.delta_path):
                shutil.rmtree(self.delta_path)
        except:
            pass
        
        try:
            if os.path.exists(self.ducklake_path):
                if os.path.isfile(self.ducklake_path):
                    os.remove(self.ducklake_path)
                else:
                    shutil.rmtree(self.ducklake_path)
        except:
            pass


def run_10m_scale_comparison():
    """Run comprehensive 10M row scale comparison."""
    comparison = Scale10MComparison()
    
    try:
        print("🚀 Starting 10M Row Scale Comparison")
        print("=" * 80)
        
        # Setup dataset
        metadata = comparison.setup_10m_dataset()
        
        # Setup Delta Lake
        dt = comparison.setup_delta_table(metadata)
        
        # Setup DuckLake
        conn = comparison.setup_ducklake_table(metadata)
        
        # Run performance tests
        comparison.test_delta_native_performance(dt, metadata)
        comparison.test_ducklake_performance(conn, metadata)
        
        # Analyze results
        comparison.analyze_10m_results()
        
        # Close connection
        conn.close()
        
    except Exception as e:
        print(f"❌ 10M scale comparison failed: {e}")
        raise
    
    finally:
        comparison.cleanup()


if __name__ == "__main__":
    run_10m_scale_comparison()
