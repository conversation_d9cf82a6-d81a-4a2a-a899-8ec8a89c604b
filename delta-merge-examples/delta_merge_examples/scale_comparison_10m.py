"""
10 Million Row Scale Comparison: Delta Lake Native vs DuckLake
Focused performance comparison at production scale.
"""

import os
import shutil
import time
import random
from pathlib import Path
import pyarrow as pa
import pyarrow.parquet as pq
import duckdb
from deltalake import DeltaTable, write_deltalake

from delta_merge_examples.shared_data_generator import (
    ParquetDatasetManager,
    create_batch_test_data
)
from delta_merge_examples.performance_profiler import (
    DeltaLakeProfiler, 
    DuckLakeProfiler
)


class Scale10MComparison:
    """Compare Delta Lake Native vs DuckLake at 10M row scale."""
    
    def __init__(self):
        self.dataset_manager = ParquetDatasetManager()
        self.delta_path = "./scale_10m_delta"
        self.ducklake_path = "./scale_10m_ducklake"
        self.results = {}
    
    def setup_10m_dataset(self):
        """Set up 10M row dataset for testing."""
        dataset_name = "scale_10m_test"
        
        if not self.dataset_manager.dataset_exists(dataset_name):
            print("📦 Generating 10M row dataset (this may take a few minutes)...")
            print("   🎯 Target: 5,000 files, 10,000,000 rows")
            print("   📊 Distribution: ~100 large files (50K-200K rows), 4,900 small files")
            
            metadata = self.dataset_manager.generate_dataset(
                dataset_name=dataset_name,
                total_files=5000,
                total_rows=10000000,
                large_file_ratio=1/50  # 2% large files, 98% small files
            )
        else:
            print("📦 Loading existing 10M row dataset...")
            metadata = self.dataset_manager.load_dataset_metadata(dataset_name)
        
        print(f"Dataset ready: {len(metadata['parquet_files']):,} files")
        large_files = [f for f in metadata['parquet_files'] if f['is_large']]
        small_files = [f for f in metadata['parquet_files'] if not f['is_large']]
        print(f"Distribution: {len(large_files)} large files, {len(small_files)} small files")
        
        return metadata
    
    def setup_delta_table(self, metadata):
        """Set up Delta Lake table from 10M row dataset using batch-oriented loading."""
        if os.path.exists(self.delta_path):
            shutil.rmtree(self.delta_path)

        print("Setting up Delta Lake table (10M rows)...")
        start_time = time.time()

        # Use batch-oriented loading - each batch represents a realistic ETL batch
        batch_size = 100  # Realistic ETL batch size
        total_files = len(metadata['parquet_files'])

        for batch_start in range(0, total_files, batch_size):
            batch_end = min(batch_start + batch_size, total_files)
            if batch_start % 1000 == 0:  # Progress updates
                print(f"   Loading batch: files {batch_start:,}-{batch_end-1:,}")

            batch_files = metadata['parquet_files'][batch_start:batch_end]
            parquet_paths = [f['parquet_path'] for f in batch_files]

            # Delta Lake best practice: Use direct Parquet paths for efficient loading
            # This avoids loading into memory and leverages Delta's native Parquet handling
            mode = "overwrite" if batch_start == 0 else "append"

            # For multiple Parquet files, Delta Lake can efficiently handle them
            # by reading directly from paths without intermediate PyArrow conversion
            if len(parquet_paths) == 1:
                # Single file - read directly
                table = pq.read_table(parquet_paths[0])
            else:
                # Multiple files - use efficient concatenation
                # This is more memory efficient than loading all into memory
                tables = []
                for path in parquet_paths:
                    tables.append(pq.read_table(path))
                table = pa.concat_tables(tables)

            # Single transaction per batch (realistic ETL pattern)
            write_deltalake(self.delta_path, table, mode=mode)

        setup_time = time.time() - start_time
        dt = DeltaTable(self.delta_path)
        total_rows = dt.to_pyarrow_table().num_rows

        print(f"Delta table ready: {total_rows:,} rows in {setup_time:.1f}s")
        return dt
    
    def setup_ducklake_table(self, metadata):
        """Set up DuckLake table from 10M row dataset using batch transactions."""
        # Clean up existing
        if os.path.exists(self.ducklake_path):
            if os.path.isfile(self.ducklake_path):
                os.remove(self.ducklake_path)
            else:
                shutil.rmtree(self.ducklake_path)

        print("Setting up DuckLake table (10M rows)...")
        start_time = time.time()

        # Initialize DuckDB connection
        conn = duckdb.connect()
        conn.execute("INSTALL ducklake;")
        conn.execute("LOAD ducklake;")
        conn.execute(f"ATTACH 'ducklake:{self.ducklake_path}' AS scale_lake;")
        conn.execute("USE scale_lake;")

        # Create table schema
        conn.execute("""
            CREATE TABLE demo (
                file_id VARCHAR,
                customer_id VARCHAR,
                transaction_id VARCHAR,
                amount DOUBLE,
                transaction_date TIMESTAMP,
                processed_at TIMESTAMP
            );
        """)

        # Load data in proper batches with transactions
        batch_size = 100  # Realistic ETL batch size
        total_files = len(metadata['parquet_files'])

        for batch_start in range(0, total_files, batch_size):
            batch_end = min(batch_start + batch_size, total_files)
            if batch_start % 1000 == 0:  # Progress updates
                print(f"   Loading batch: files {batch_start:,}-{batch_end-1:,}")

            batch_files = metadata['parquet_files'][batch_start:batch_end]

            # DuckLake best practice: Use single transaction per batch
            # This reflects real ETL where batches of files arrive together
            conn.execute("BEGIN TRANSACTION;")

            try:
                # DuckDB can efficiently read multiple Parquet files in a single query
                # Build list of file paths for this batch
                parquet_paths = [f"'{f['parquet_path']}'" for f in batch_files]

                if len(parquet_paths) == 1:
                    # Single file in batch
                    conn.execute(f"INSERT INTO demo SELECT * FROM read_parquet({parquet_paths[0]});")
                else:
                    # Multiple files in batch - use DuckDB's efficient multi-file reading
                    # This is much more efficient than individual INSERT statements
                    paths_list = f"[{', '.join(parquet_paths)}]"
                    conn.execute(f"INSERT INTO demo SELECT * FROM read_parquet({paths_list});")

                # Commit the entire batch as one transaction
                conn.execute("COMMIT;")

            except Exception as e:
                # Rollback on any error
                conn.execute("ROLLBACK;")
                raise e

        setup_time = time.time() - start_time
        result = conn.execute("SELECT COUNT(*) FROM demo;").fetchone()
        total_rows = result[0] if result else 0

        print(f"DuckLake table ready: {total_rows:,} rows in {setup_time:.1f}s")
        return conn
    
    def test_delta_native_performance(self, dt, metadata):
        """Test Delta Lake native operations with realistic batch scenarios."""
        print(f"\n{'='*80}")
        print("DELTA LAKE NATIVE OPERATIONS - BATCH TESTING")
        print(f"{'='*80}")

        profiler = DeltaLakeProfiler(self.delta_path)

        # Test realistic batch scenarios
        test_scenarios = [
            ("small_batch", [f for f in metadata['parquet_files'] if not f['is_large']][:5]),
            ("large_batch", [f for f in metadata['parquet_files'] if f['is_large']][:3]),
            ("mixed_batch", [f for f in metadata['parquet_files'] if not f['is_large']][:3] +
                           [f for f in metadata['parquet_files'] if f['is_large']][:2])
        ]

        results = {}

        for scenario_name, test_files in test_scenarios:
            print(f"\nTesting {scenario_name} operations...")

            # Create test batch data directory
            batch_dir = f"./test_batch_{scenario_name}"

            # Generate batch test data (simulates new files arriving)
            batch_data = create_batch_test_data(
                batch_id=f"test_{scenario_name}",
                file_count=len(test_files),
                total_rows=sum(f['row_count'] for f in test_files),
                output_dir=batch_dir
            )

            # Get file IDs to replace
            file_ids_to_replace = [f['file_id'] for f in test_files]

            print(f"   Batch: {len(test_files)} files, {sum(f['row_count'] for f in test_files):,} total rows")

            # Profile batch DELETE + APPEND operations
            with profiler.profile_operation(f"Delta Batch {scenario_name}"):
                batch_start = time.time()

                # DELETE: Remove all files in batch
                delete_start = time.time()
                for file_id in file_ids_to_replace:
                    dt.delete(predicate=f"file_id = '{file_id}'")
                delete_time = time.time() - delete_start

                # APPEND: Add all new files in batch
                append_start = time.time()
                # Read all Parquet files in the batch
                tables = []
                for parquet_file in batch_data['parquet_files']:
                    tables.append(pq.read_table(parquet_file))

                if tables:
                    combined_table = pa.concat_tables(tables)
                    write_deltalake(self.delta_path, combined_table, mode="append")

                append_time = time.time() - append_start
                total_time = time.time() - batch_start

            # Clean up test batch directory
            import shutil
            if os.path.exists(batch_dir):
                shutil.rmtree(batch_dir)

            scenario_result = {
                'scenario': scenario_name,
                'file_count': len(test_files),
                'total_rows': sum(f['row_count'] for f in test_files),
                'delete_time': delete_time,
                'append_time': append_time,
                'total_time': total_time,
                'profile': profiler.current_profile
            }

            results[scenario_name] = scenario_result
            print(f"      DELETE: {delete_time:.3f}s, APPEND: {append_time:.3f}s, Total: {total_time:.3f}s")

        self.results['delta_native'] = results
        return results
    
    def test_ducklake_performance(self, conn, metadata):
        """Test DuckLake operations with realistic batch scenarios."""
        print(f"\n{'='*80}")
        print("DUCKLAKE OPERATIONS - BATCH TESTING")
        print(f"{'='*80}")

        profiler = DuckLakeProfiler(conn)

        # Test realistic batch scenarios (same as Delta Lake for fair comparison)
        test_scenarios = [
            ("small_batch", [f for f in metadata['parquet_files'] if not f['is_large']][:5]),
            ("large_batch", [f for f in metadata['parquet_files'] if f['is_large']][:3]),
            ("mixed_batch", [f for f in metadata['parquet_files'] if not f['is_large']][:3] +
                           [f for f in metadata['parquet_files'] if f['is_large']][:2])
        ]

        results = {}

        for scenario_name, test_files in test_scenarios:
            print(f"\nTesting {scenario_name} operations...")

            # Create test batch data directory
            batch_dir = f"./test_batch_{scenario_name}_duck"

            # Generate batch test data (simulates new files arriving)
            batch_data = create_batch_test_data(
                batch_id=f"test_{scenario_name}_duck",
                file_count=len(test_files),
                total_rows=sum(f['row_count'] for f in test_files),
                output_dir=batch_dir
            )

            # Get file IDs to replace
            file_ids_to_replace = [f['file_id'] for f in test_files]

            print(f"   Batch: {len(test_files)} files, {sum(f['row_count'] for f in test_files):,} total rows")

            # Profile batch transaction
            with profiler.profile_operation(f"DuckLake Batch {scenario_name}"):
                transaction_start = time.time()

                # Begin single transaction for entire batch
                conn.execute("BEGIN TRANSACTION;")

                try:
                    # DELETE: Remove all files in batch
                    delete_start = time.time()
                    for file_id in file_ids_to_replace:
                        conn.execute(f"DELETE FROM demo WHERE file_id = '{file_id}';")
                    delete_time = time.time() - delete_start

                    # INSERT: Add all new files in batch using efficient multi-file read
                    insert_start = time.time()
                    parquet_paths = [f"'{f}'" for f in batch_data['parquet_files']]

                    if len(parquet_paths) == 1:
                        # Single file
                        conn.execute(f"INSERT INTO demo SELECT * FROM read_parquet({parquet_paths[0]});")
                    else:
                        # Multiple files - use DuckDB's efficient multi-file reading
                        paths_list = f"[{', '.join(parquet_paths)}]"
                        conn.execute(f"INSERT INTO demo SELECT * FROM read_parquet({paths_list});")

                    insert_time = time.time() - insert_start

                    # COMMIT: Commit entire batch
                    commit_start = time.time()
                    conn.execute("COMMIT;")
                    commit_time = time.time() - commit_start

                    total_time = time.time() - transaction_start

                except Exception as e:
                    # Rollback on error
                    conn.execute("ROLLBACK;")
                    raise e

            # Clean up test batch directory
            import shutil
            if os.path.exists(batch_dir):
                shutil.rmtree(batch_dir)

            scenario_result = {
                'scenario': scenario_name,
                'file_count': len(test_files),
                'total_rows': sum(f['row_count'] for f in test_files),
                'delete_time': delete_time,
                'insert_time': insert_time,
                'commit_time': commit_time,
                'total_time': total_time,
                'profile': profiler.current_profile
            }

            results[scenario_name] = scenario_result
            print(f"      DELETE: {delete_time:.3f}s, INSERT: {insert_time:.3f}s, COMMIT: {commit_time:.3f}s, Total: {total_time:.3f}s")

        self.results['ducklake'] = results
        return results
    
    def analyze_10m_results(self):
        """Analyze and compare batch-oriented performance results."""
        print(f"\n{'='*80}")
        print("BATCH PERFORMANCE ANALYSIS")
        print(f"{'='*80}")

        if 'delta_native' not in self.results or 'ducklake' not in self.results:
            print("Incomplete results for analysis")
            return

        # Compare by batch scenario
        scenarios = ['small_batch', 'large_batch', 'mixed_batch']

        print(f"\nBATCH PERFORMANCE COMPARISON")
        print("-" * 80)
        print(f"{'Scenario':<15} {'Files':<8} {'Rows':<12} {'Delta (s)':<12} {'DuckLake (s)':<12} {'Winner':<12}")
        print("-" * 80)

        delta_total = 0
        ducklake_total = 0
        delta_wins = 0
        ducklake_wins = 0

        for scenario in scenarios:
            if scenario in self.results['delta_native'] and scenario in self.results['ducklake']:
                delta_result = self.results['delta_native'][scenario]
                ducklake_result = self.results['ducklake'][scenario]

                delta_time = delta_result['total_time']
                ducklake_time = ducklake_result['total_time']

                winner = "Delta" if delta_time < ducklake_time else "DuckLake"
                if delta_time < ducklake_time:
                    delta_wins += 1
                else:
                    ducklake_wins += 1

                delta_total += delta_time
                ducklake_total += ducklake_time

                print(f"{scenario:<15} {delta_result['file_count']:<8} {delta_result['total_rows']:<12,} "
                      f"{delta_time:<12.3f} {ducklake_time:<12.3f} {winner:<12}")

        # Overall comparison
        print("-" * 80)
        print(f"{'TOTAL':<15} {'':<8} {'':<12} {delta_total:<12.3f} {ducklake_total:<12.3f}")

        overall_winner = "Delta Lake" if delta_total < ducklake_total else "DuckLake"
        speedup = max(delta_total, ducklake_total) / min(delta_total, ducklake_total)

        print(f"\nOVERALL RESULTS:")
        print(f"Delta Lake total time: {delta_total:.3f}s")
        print(f"DuckLake total time:   {ducklake_total:.3f}s")
        print(f"Overall winner:        {overall_winner} ({speedup:.2f}x faster)")
        print(f"Delta Lake wins:       {delta_wins}/{len(scenarios)}")
        print(f"DuckLake wins:         {ducklake_wins}/{len(scenarios)}")

        # Technical summary
        print(f"\nTECHNICAL SUMMARY:")
        print("1. Batch processing performance at production scale")
        print("2. Multi-file transaction efficiency")
        print("3. Memory usage patterns for batch operations")
        print("4. I/O efficiency differences")
        print("5. Transaction model: Delta (multiple operations) vs DuckLake (single transaction)")
    
    def cleanup(self):
        """Clean up test resources."""
        try:
            if os.path.exists(self.delta_path):
                shutil.rmtree(self.delta_path)
        except:
            pass
        
        try:
            if os.path.exists(self.ducklake_path):
                if os.path.isfile(self.ducklake_path):
                    os.remove(self.ducklake_path)
                else:
                    shutil.rmtree(self.ducklake_path)
        except:
            pass


def run_10m_scale_comparison():
    """Run comprehensive 10M row scale comparison."""
    comparison = Scale10MComparison()
    
    try:
        print("🚀 Starting 10M Row Scale Comparison")
        print("=" * 80)
        
        # Setup dataset
        metadata = comparison.setup_10m_dataset()
        
        # Setup Delta Lake
        dt = comparison.setup_delta_table(metadata)
        
        # Setup DuckLake
        conn = comparison.setup_ducklake_table(metadata)
        
        # Run performance tests
        comparison.test_delta_native_performance(dt, metadata)
        comparison.test_ducklake_performance(conn, metadata)
        
        # Analyze results
        comparison.analyze_10m_results()
        
        # Close connection
        conn.close()
        
    except Exception as e:
        print(f"❌ 10M scale comparison failed: {e}")
        raise
    
    finally:
        comparison.cleanup()


if __name__ == "__main__":
    run_10m_scale_comparison()
