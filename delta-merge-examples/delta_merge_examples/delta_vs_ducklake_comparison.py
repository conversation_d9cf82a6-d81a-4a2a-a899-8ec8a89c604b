"""
Comprehensive comparison between Delta Lake and DuckLake using identical test scenarios.
This script runs the same operations on both systems and provides detailed comparisons.
"""

import os
import time
from pathlib import Path
from delta_merge_examples.scale_analysis import ScaleAnalysis
from delta_merge_examples.ducklake_analysis import DuckLakeAnalysis


class DeltaVsDuckLakeComparison:
    """Compare Delta Lake and DuckLake performance using identical test scenarios."""
    
    def __init__(self):
        self.delta_analyzer = ScaleAnalysis("./comparison_delta_table")
        self.ducklake_analyzer = DuckLakeAnalysis("./comparison_ducklake")
        self.results = {}
    
    def cleanup(self):
        """Clean up both analyzers."""
        try:
            self.delta_analyzer.cleanup()
        except:
            pass
        try:
            self.ducklake_analyzer.cleanup()
        except:
            pass
    
    def setup_identical_datasets(self, total_files: int = 500, total_rows: int = 25000, batch_size: int = 25):
        """Set up identical datasets in both Delta Lake and DuckLake."""
        print("🏗️  Setting up identical datasets...")
        print(f"   📊 {total_files} files, {total_rows:,} rows, batch size: {batch_size}")
        
        # Setup Delta Lake
        print("   🔺 Setting up Delta Lake...")
        delta_start = time.time()
        self.delta_analyzer.setup_realistic_table_in_batches(total_files, total_rows, batch_size)
        delta_setup_time = time.time() - delta_start
        
        # Setup DuckLake
        print("   🦆 Setting up DuckLake...")
        ducklake_start = time.time()
        self.ducklake_analyzer.setup_ducklake()
        self.ducklake_analyzer.setup_realistic_table_in_batches(total_files, total_rows, batch_size)
        ducklake_setup_time = time.time() - ducklake_start
        
        print(f"Setup complete:")
        print(f"   Delta Lake setup: {delta_setup_time:.2f}s")
        print(f"   DuckLake setup: {ducklake_setup_time:.2f}s")
        
        return {
            'delta_setup_time': delta_setup_time,
            'ducklake_setup_time': ducklake_setup_time
        }
    
    def compare_append_operations(self):
        """Compare APPEND operations between Delta Lake and DuckLake."""
        print(f"\n{'='*80}")
        print("COMPARISON: APPEND Operations")
        print(f"{'='*80}")
        
        # Delta Lake APPEND
        print("🔺 Testing Delta Lake APPEND...")
        delta_result = self.delta_analyzer.test_append_comparison(
            self.delta_analyzer.setup_large_table(500)
        )
        
        # DuckLake APPEND
        print("🦆 Testing DuckLake APPEND...")
        ducklake_result = self.ducklake_analyzer.test_append_operation()
        
        # Compare results
        self.analyze_comparison("APPEND", delta_result, ducklake_result)
        
        return {
            'delta': delta_result,
            'ducklake': ducklake_result
        }
    
    def compare_delete_operations(self):
        """Compare DELETE operations between Delta Lake and DuckLake."""
        print(f"\n{'='*80}")
        print("COMPARISON: DELETE Operations")
        print(f"{'='*80}")
        
        # Delta Lake DELETE
        print("🔺 Testing Delta Lake DELETE...")
        delta_result = self.delta_analyzer.test_delete_comparison(
            self.delta_analyzer.setup_large_table(500)
        )
        
        # DuckLake DELETE
        print("🦆 Testing DuckLake DELETE...")
        ducklake_result = self.ducklake_analyzer.test_delete_operation()
        
        # Compare results
        self.analyze_comparison("DELETE", delta_result, ducklake_result)
        
        return {
            'delta': delta_result,
            'ducklake': ducklake_result
        }
    
    def compare_update_operations(self):
        """Compare UPDATE operations between Delta Lake and DuckLake."""
        print(f"\n{'='*80}")
        print("COMPARISON: UPDATE Operations")
        print(f"{'='*80}")
        
        # Delta Lake UPDATE
        print("🔺 Testing Delta Lake UPDATE...")
        delta_result = self.delta_analyzer.test_update_comparison(
            self.delta_analyzer.setup_large_table(500)
        )
        
        # DuckLake UPDATE
        print("🦆 Testing DuckLake UPDATE...")
        ducklake_result = self.ducklake_analyzer.test_update_operation()
        
        # Compare results
        self.analyze_comparison("UPDATE", delta_result, ducklake_result)
        
        return {
            'delta': delta_result,
            'ducklake': ducklake_result
        }
    
    def analyze_comparison(self, operation: str, delta_result: dict, ducklake_result: dict):
        """Analyze and display comparison between Delta Lake and DuckLake results."""
        print(f"\n📊 COMPARISON ANALYSIS: {operation}")
        print("-" * 70)
        
        # Extract results for comparison
        if operation == "APPEND":
            delta_data = delta_result.get('native', {}) if delta_result else {}
            ducklake_data = ducklake_result or {}
        elif operation == "DELETE":
            delta_data = delta_result.get('native', {}) if delta_result else {}
            ducklake_data = ducklake_result or {}
        elif operation == "UPDATE":
            delta_data = delta_result.get('native', {}) if delta_result else {}
            ducklake_data = ducklake_result or {}
        else:
            delta_data = {}
            ducklake_data = {}
        
        if delta_data.get('success') and ducklake_data.get('success'):
            delta_time = delta_data['execution_time']
            ducklake_time = ducklake_data['execution_time']
            delta_memory = delta_data.get('memory_delta', 0)
            ducklake_memory = ducklake_data.get('memory_delta', 0)
            
            print(f"⏱️  EXECUTION TIME:")
            print(f"   🔺 Delta Lake: {delta_time:.3f}s")
            print(f"   🦆 DuckLake:   {ducklake_time:.3f}s")
            
            if delta_time < ducklake_time:
                speedup = ducklake_time / delta_time
                print(f"   Delta Lake is {speedup:.2f}x faster")
            else:
                speedup = delta_time / ducklake_time
                print(f"   DuckLake is {speedup:.2f}x faster")
            
            print(f"\n💾 MEMORY USAGE:")
            print(f"   🔺 Delta Lake: {delta_memory:+.1f} MB")
            print(f"   🦆 DuckLake:   {ducklake_memory:+.1f} MB")
            
            print(f"\n📝 TRANSACTION MODEL:")
            delta_versions = delta_data.get('version_increment', 1)
            print(f"   🔺 Delta Lake: {delta_versions} version(s)")
            print(f"   🦆 DuckLake:   1 snapshot (SQL transaction)")
            
        else:
            if not delta_data.get('success'):
                print(f"❌ Delta Lake {operation} failed: {delta_data.get('error', 'Unknown error')}")
            if not ducklake_data.get('success'):
                print(f"❌ DuckLake {operation} failed: {ducklake_data.get('error', 'Unknown error')}")
    
    def run_comprehensive_comparison(self):
        """Run comprehensive comparison between Delta Lake and DuckLake."""
        print("🚀 Starting Comprehensive Delta Lake vs DuckLake Comparison")
        print("="*80)
        
        try:
            # Setup identical datasets
            setup_results = self.setup_identical_datasets()
            self.results['setup'] = setup_results
            
            # Compare APPEND operations
            append_results = self.compare_append_operations()
            self.results['append'] = append_results
            
            # Compare DELETE operations
            delete_results = self.compare_delete_operations()
            self.results['delete'] = delete_results
            
            # Compare UPDATE operations
            update_results = self.compare_update_operations()
            self.results['update'] = update_results
            
            # Print final summary
            self.print_final_summary()
            
        except Exception as e:
            print(f"❌ Comparison failed: {e}")
            raise
        finally:
            self.cleanup()
    
    def print_final_summary(self):
        """Print comprehensive final summary of all comparisons."""
        print("\n" + "="*80)
        print("FINAL COMPARISON SUMMARY: Delta Lake vs DuckLake")
        print("="*80)
        
        print(f"\n📊 Performance Summary:")
        print(f"{'Operation':<12} {'Delta (s)':<12} {'DuckLake (s)':<12} {'Winner':<12} {'Speedup':<10}")
        print("-" * 70)
        
        delta_wins = 0
        ducklake_wins = 0
        
        for operation in ['append', 'delete', 'update']:
            if operation in self.results:
                result = self.results[operation]
                delta_data = result.get('delta', {})
                ducklake_data = result.get('ducklake', {})
                
                # Extract the appropriate data based on operation
                if operation == 'append':
                    delta_perf = delta_data.get('native', {}) if delta_data else {}
                elif operation == 'delete':
                    delta_perf = delta_data.get('native', {}) if delta_data else {}
                elif operation == 'update':
                    delta_perf = delta_data.get('native', {}) if delta_data else {}
                else:
                    delta_perf = {}
                
                if delta_perf.get('success') and ducklake_data.get('success'):
                    delta_time = delta_perf['execution_time']
                    ducklake_time = ducklake_data['execution_time']
                    
                    if delta_time < ducklake_time:
                        winner = "Delta"
                        speedup = f"{ducklake_time/delta_time:.2f}x"
                        delta_wins += 1
                    else:
                        winner = "DuckLake"
                        speedup = f"{delta_time/ducklake_time:.2f}x"
                        ducklake_wins += 1
                    
                    print(f"{operation.upper():<12} {delta_time:<12.3f} {ducklake_time:<12.3f} {winner:<12} {speedup:<10}")
                else:
                    print(f"{operation.upper():<12} {'FAILED':<12} {'FAILED':<12} {'N/A':<12} {'N/A':<10}")
        
        print(f"\nOverall Performance:")
        print(f"   Delta Lake wins: {delta_wins}")
        print(f"   DuckLake wins: {ducklake_wins}")

        print(f"\nKEY DIFFERENCES:")
        print("1. Delta Lake: 2 separate operations (DELETE + APPEND)")
        print("2. DuckLake: 1 transaction (DELETE + INSERT + COMMIT)")
        print("3. Memory usage patterns differ")
        print("4. Performance characteristics vary by operation type")
        print("5. Transaction isolation models differ")


def run_delta_vs_ducklake_comparison():
    """Run the comprehensive Delta Lake vs DuckLake comparison."""
    comparison = DeltaVsDuckLakeComparison()
    comparison.run_comprehensive_comparison()


if __name__ == "__main__":
    run_delta_vs_ducklake_comparison()
